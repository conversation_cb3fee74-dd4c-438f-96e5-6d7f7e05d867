from ai_utility_orchestrator.core.tools import Tool
import openai
import os

openai.api_key = os.getenv("OPENAI_API_KEY")

def summarizer_function(input_text):
    prompt = f"Summarize the following text:\n\n{input_text}"
    response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[{"role": "user", "content": prompt}],
        temperature=0.5,
        max_tokens=300
    )
    return response.choices[0].message['content'].strip()

class SummarizerTool(Tool):
    def __init__(self):
        super().__init__(
            name="summarizer",
            description="Summarizes a given block of text using OpenAI.",
            execute_func=summarizer_function,
            schema={"type": "text", "required": True}
        )