{"llm": {"model": "gpt-4o-mini", "temperature": 0.5, "max_tokens": 800}, "system_prompt": "You are an AI Orchestrator that helps users by routing queries to appropriate tools.", "default_user_id": "default_user", "context_limit": 3, "enable_parameter_enhancement": true, "enable_ai_response_generation": true, "enable_dynamic_tool_discovery": true, "enable_ai_error_recovery": true, "enable_context_aware_routing": true, "context_storage": {"backend": "file", "file_path": null}, "context_format": {"user_role": "user", "assistant_role": "assistant", "include_metadata": false}, "registry_config": {"registration_message": "Registered tool: {tool_name}"}, "parameter_enhancement": {"temperature": 0.3}, "json_extraction": {"patterns": ["\\{.*?\\}", "```json\\s*(\\{.*?\\})\\s*```", "\\{[^{}]*\"selected_tool\"[^{}]*\\}"]}, "log_messages": {"start": " Starting agent_executor...", "tool_registered": " Registered tool: {tool_name}", "tool_import_failed": " Could not import {tool_name}: {error}", "tool_selection": " Asking LLM to select tool...", "tool_execution": "Executing tool: {tool_name}", "direct_response": " Providing direct response"}, "ai_prompt_generation": {"enabled": true, "context_aware": true, "adaptive_complexity": true}, "prompt_templates": {"tool_selection": "You are an intelligent AI Orchestrator with advanced reasoning capabilities. Analyze the user's request contextually and determine the optimal tool to use.\n\nAvailable tools:\n{tools_text}\n\nUser request: {user_input}{context_text}\n\nConsider:\n- User's intent and context\n- Tool capabilities and limitations\n- Previous interactions and patterns\n- Optimal parameter selection\n\nIMPORTANT: Respond with ONLY a valid JSON object.\n\nRequired JSON format:\n{{\"selected_tool\": \"tool_name_or_none\", \"parameters\": {{}}, \"reasoning\": \"detailed_explanation\", \"confidence\": 0.95, \"direct_response\": \"answer_if_no_tool_needed\"}}\n\nRespond with JSON only:", "final_response": "As an intelligent AI assistant, provide a comprehensive and helpful response based on the tool execution result.\n\nContext:\n- User's original question: {user_input}\n- Tool used: {selected_tool_name}\n- Tool result: {tool_result}\n- User context: {user_context}\n\nProvide a natural, informative response that:\n1. Directly addresses the user's question\n2. Incorporates the tool results meaningfully\n3. Offers additional insights when relevant\n4. Maintains conversational flow\n\nResponse:", "error_recovery": "An error occurred, but I can help resolve this intelligently.\n\nError context:\n- Original request: {user_input}\n- Attempted tool: {tool_name}\n- Error: {error}\n- Available alternatives: {alternatives}\n\nLet me suggest the best alternative approach or provide a direct helpful response.", "parameter_enhancement": "Optimize these tool parameters for maximum effectiveness:\n\nTool: {tool_name}\nOriginal parameters: {original_params}\nUser context: {user_context}\nTool capabilities: {tool_schema}\n\nProvide enhanced parameters as JSON that will improve results while maintaining user intent."}, "error_messages": {"tool_execution_failed": "I tried to use the {tool_name} tool, but encountered an error: {error}", "tool_not_found": "I wanted to use the {tool_name} tool, but it's not available. Let me try to help you directly: {fallback_response}", "general_error": "I apologize, but I encountered an error while processing your request: {error}", "parse_error": "Could not understand how to process your request"}, "default_responses": {"no_tool_needed": "I can help you with that, but I'm not sure how to respond."}, "dynamic_tool_discovery": {"enabled": true, "auto_register_core_tools": true, "scan_paths": ["core.tools", "custom_tools"], "tool_patterns": ["*_execute", "*_tool"]}, "tools": [{"name": "intelligent_search", "description": "AI-powered search tool with context awareness and intelligent result generation", "execute_func": "core.tools.search_execute", "category": "information", "ai_enhanced": true, "schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query with natural language support"}, "search_type": {"type": "string", "enum": ["web", "academic", "news", "technical", "social"], "default": "web"}, "max_results": {"type": "integer", "default": 5, "minimum": 1, "maximum": 20}, "context_aware": {"type": "boolean", "default": true, "description": "Use conversation context for better results"}}, "required": ["query"]}}, {"name": "tool_explorer", "description": "Intelligent tool registry explorer with AI-powered recommendations and usage guidance", "execute_func": "core.tools.registry_query_tool_execute", "category": "system", "ai_enhanced": true, "schema": {"type": "object", "properties": {"output_format": {"type": "string", "enum": ["intelligent", "detailed", "json", "list"], "default": "intelligent"}, "include_descriptions": {"type": "boolean", "default": true}, "category_filter": {"type": "string", "description": "Filter tools by category"}, "recommendation_mode": {"type": "boolean", "default": true, "description": "Provide AI-powered tool recommendations"}}}}]}