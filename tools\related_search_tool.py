from ai_utility_orchestrator.core.tools import Tool
import openai
import os

openai.api_key = os.getenv("OPENAI_API_KEY")

def related_search_function(input_text):
    prompt = f"Given the following text, suggest related real-world topics or news headlines:\n\n{input_text}"
    response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[{"role": "user", "content": prompt}],
        temperature=0.7,
        max_tokens=300
    )
    return response.choices[0].message['content'].strip()

class RelatedSearchTool(Tool):
    def __init__(self):
        super().__init__(
            name="related_search",
            description="Finds related topics or news headlines based on input text using OpenAI.",
            execute_func=related_search_function,
            schema={"type": "text", "required": True}
        )