from typing import Callable, Dict, Any
import json
import logging

logger = logging.getLogger(__name__)

# Import format_response at module level to avoid repeated imports
try:
    from ai_utility_orchestrator.utils.response_formatter import format_response
except ImportError:
    # Fallback for development/testing
    format_response = None

class Tool:
    def __init__(self, name: str, description: str, execute_func: Callable, schema: Dict[str, Any],
                 category: str = "general", ai_enhanced: bool = False):
        self.name = name
        self.description = description
        self.execute = execute_func
        self.schema = schema
        self.category = category
        self.ai_enhanced = ai_enhanced



def search_execute(params: Dict[str, Any]) -> str:
    """AI-powered dynamic search tool with intelligent response generation."""
    query = params.get("query", "")
    search_type = params.get("search_type", "web")
    max_results = params.get("max_results", 5)
    config = params.get("config", {})
    context_manager = params.get("context_manager")
    user_id = params.get("user_id", "unknown")

    # Store search context for future reference
    if context_manager:
        search_history = context_manager.get_context("search_history", [])
        search_history.append({"query": query, "type": search_type, "user": user_id})
        context_manager.set_context("search_history", search_history[-10:])  # Keep last 10
        context_manager.set_context("last_search", {"query": query, "type": search_type})

    # AI-driven response generation (enabled by default in new config)
    if config.get("enable_ai_response_generation", True) and format_response:

        # Enhanced AI prompt with context awareness
        user_context = ""
        if context_manager:
            previous_searches = len(context_manager.get_context("search_history", []))
            user_context = f"User has performed {previous_searches} previous searches."

        template_prompt = f"""Generate comprehensive, realistic search results for this query:

Search Details:
- Query: "{query}"
- Search Type: {search_type}
- Max Results: {max_results}
- User Context: {user_context}

Requirements:
1. Create {max_results} realistic, relevant results
2. Include titles, brief descriptions, and insights
3. Tailor content to {search_type} search type
4. Make results specific and actionable
5. Consider user's search context and intent

Provide a comprehensive response that simulates real {search_type} search results with detailed, helpful information."""

        try:
            ai_response = format_response(
                prompt=template_prompt,
                formatter="answer",
                model_name=config.get("llm", {}).get("model", "gpt-4o-mini"),
                temperature=0.6,  # Balanced creativity for search results
                system_prompt="You are an intelligent search engine that provides comprehensive, accurate results."
            )
            if ai_response:
                return ai_response
        except Exception as e:
            logger.warning(f"AI search generation failed: {e}")

    # Enhanced fallback with dynamic intelligence
    response_template = params.get("response_template", "Search results for: {query}")

    # Intelligent response modifiers based on search type
    modifiers = {
        "academic": f" Found {max_results} scholarly articles and research papers",
        "news": f"  Latest {max_results} news articles and updates",
        "web": f"  Top {max_results} web results with comprehensive information",
        "technical": f"{max_results} technical resources and documentation",
        "social": f" {max_results} social media insights and discussions"
    }

    base_response = response_template.format(query=query)
    modifier = modifiers.get(search_type, f" ({max_results} results)")

    # Add context-aware enhancement
    if context_manager and context_manager.has_context("search_history"):
        history_count = len(context_manager.get_context("search_history", []))
        if history_count > 1:
            base_response += f" (Building on your {history_count} previous searches)"

    return base_response + modifier

def registry_query_tool_execute(params: Dict[str, Any]) -> str:
    """AI-powered registry query tool with intelligent, context-aware responses."""
    registry = params.get("registry")
    output_format = params.get("output_format", "intelligent")
    include_descriptions = params.get("include_descriptions", True)
    config = params.get("config", {})
    original_query = params.get("original_query", "")
    context_manager = params.get("context_manager")
    user_id = params.get("user_id", "unknown")

    if not registry:
        return "Tool registry is not available. Please check system configuration."

    tool_names = registry.list_tools()

    # Store registry query context
    if context_manager:
        query_history = context_manager.get_context("registry_queries", [])
        query_history.append({"query": original_query, "user": user_id, "tool_count": len(tool_names)})
        context_manager.set_context("registry_queries", query_history[-5:])  # Keep last 5

    # AI-driven intelligent response generation (enabled by default)
    if config.get("enable_ai_response_generation", True) and original_query and format_response:

        # Build comprehensive tool information for AI
        tools_info = []
        for name in tool_names:
            tool = registry.get_tool(name)
            if tool:
                schema_summary = "No parameters" if not tool.schema else f"Parameters: {list(tool.schema.get('properties', {}).keys())}"
                tools_info.append(f"{name}: {tool.description}\n   {schema_summary}")
            else:
                tools_info.append(f"{name}: (Description not available)")

        tools_text = "\n".join(tools_info)

        # Add user context
        user_context = ""
        if context_manager:
            previous_queries = len(context_manager.get_context("registry_queries", []))
            if previous_queries > 1:
                user_context = f"User has queried the registry {previous_queries} times."

        ai_prompt = f"""The user asked: "{original_query}"

Available Tools ({len(tool_names)} total):
{tools_text}

User Context: {user_context}

Provide an intelligent, helpful response that:
1. Directly addresses the user's question
2. Explains relevant tools and their capabilities
3. Suggests which tools might be most useful for their needs
4. Uses a conversational, friendly tone
5. Includes practical usage examples when relevant

Format preference: {output_format}"""

        try:
            ai_response = format_response(
                prompt=ai_prompt,
                formatter="answer",
                model_name=config.get("llm", {}).get("model", "gpt-4o-mini"),
                temperature=0.5,  # Balanced for informative responses
                system_prompt="You are a helpful AI assistant that explains available tools clearly and suggests the best options for users."
            )
            if ai_response:
                return ai_response
        except Exception as e:
            logger.warning(f"AI registry response generation failed: {e}")

    # Enhanced fallback responses with intelligence
    if output_format == "json":
        tools_info = []
        for name in tool_names:
            tool = registry.get_tool(name)
            tool_info = {"name": name, "available": True}
            if include_descriptions and tool:
                tool_info["description"] = tool.description
                tool_info["parameters"] = list(tool.schema.get("properties", {}).keys()) if tool.schema else []
            tools_info.append(tool_info)
        return json.dumps(tools_info, indent=2)

    elif output_format == "detailed":
        result = f" Available Tools ({len(tool_names)} total):\n\n"
        for i, name in enumerate(tool_names, 1):
            tool = registry.get_tool(name)
            result += f"{i}.  {name}"
            if include_descriptions and tool:
                result += f"\n    {tool.description}"
                if tool.schema and tool.schema.get("properties"):
                    params = list(tool.schema["properties"].keys())
                    result += f"\n    Parameters: {', '.join(params)}"
            result += "\n\n"
        return result.strip()

    else:
        # Intelligent default format
        if len(tool_names) == 0:
            return " No tools are currently available in the registry."
        elif len(tool_names) == 1:
            return f" Currently available tool: {tool_names[0]}"
        else:
            list_template = params.get("list_template", " Available tools ({count}): {tools}")
            return list_template.format(tools=', '.join(tool_names), count=len(tool_names))
