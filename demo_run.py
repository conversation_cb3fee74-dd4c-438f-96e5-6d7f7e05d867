import os
from ai_utility_orchestrator.core import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, agent_executor
from ai_utility_orchestrator.utils import Context<PERSON>anager, format_response

from tools.summarizer_tool import SummarizerTool
from tools.related_search_tool import RelatedSearchTool

# Step 1: Create registry and register tools
registry = ToolRegistry()
registry.register_tool(SummarizerTool())
registry.register_tool(RelatedSearchTool())

# Step 2: Load article content
with open("data/article.txt", "r") as f:
    article_text = f.read()

user_query = "Summarize this article and find related topics"

# Step 3: Setup context
context = ContextManager()
context.set_context("original_text", article_text)

# Step 4: Agent-driven execution
print("🔁 Running agent_executor with OpenAI tools...\n")
result = agent_executor(user_query, context=context)
formatted_result = format_response(result)

# Display final result
print("✅ Final Agent Response:\n")
print(formatted_result)

# Step 5: (Optional) Tool-level execution
print("\n🔍 Executing tools directly:\n")
print("🧠 Summary Tool Output:")
print(SummarizerTool().execute(article_text))
print("\n🌐 Related Search Tool Output:")
print(RelatedSearchTool().execute(article_text))